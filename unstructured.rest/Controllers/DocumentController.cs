using Microsoft.AspNetCore.Mvc;
using unstructured.rest.DTOs;
using unstructured.rest.Models;
using unstructured.rest.Services;

namespace unstructured.rest.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class DocumentController : ControllerBase
{
    private readonly IDocumentProcessingService _documentProcessingService;
    private readonly ILogger<DocumentController> _logger;

    public DocumentController(
        IDocumentProcessingService documentProcessingService,
        ILogger<DocumentController> logger)
    {
        _documentProcessingService = documentProcessingService;
        _logger = logger;
    }

    /// <summary>
    /// Process a document file and extract structured information
    /// </summary>
    /// <param name="request">File upload request containing the document and processing options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Structured document data in Azure Cognitive Search indexer format</returns>
    /// <response code="200">Document processed successfully</response>
    /// <response code="400">Invalid file or request parameters</response>
    /// <response code="500">Internal server error during processing</response>
    [HttpPost("process")]
    [ProducesResponseType(typeof(DocumentProcessingResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DocumentProcessingResult>> ProcessDocument(
        [FromForm] FileUploadRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing document: {FileName} ({FileSize} bytes)", 
                request.File?.FileName, request.File?.Length);

            var result = await _documentProcessingService.ProcessDocumentAsync(request, cancellationToken);

            _logger.LogInformation("Successfully processed document: {FileName}", request.File?.FileName);

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid request for document processing: {Message}", ex.Message);
            return BadRequest(new ProblemDetails
            {
                Title = "Invalid Request",
                Detail = ex.Message,
                Status = StatusCodes.Status400BadRequest
            });
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "External service error during document processing");
            return StatusCode(StatusCodes.Status502BadGateway, new ProblemDetails
            {
                Title = "External Service Error",
                Detail = "Failed to communicate with document processing service",
                Status = StatusCodes.Status502BadGateway
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during document processing");
            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Internal Server Error",
                Detail = "An unexpected error occurred while processing the document",
                Status = StatusCodes.Status500InternalServerError
            });
        }
    }

}
