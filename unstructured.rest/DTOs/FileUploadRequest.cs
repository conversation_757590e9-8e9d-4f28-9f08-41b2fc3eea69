using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace unstructured.rest.DTOs;

public class FileUploadRequest
{
    [Required]
    public IFormFile File { get; set; } = null!;

    [DefaultValue("auto")]
    public string Strategy { get; set; } = "auto";

    [DefaultValue(false)]
    public bool IncludeCoordinates { get; set; }

    [DefaultValue(false)]
    public bool InferTableStructure { get; set; }

    [DefaultValue(false)]
    public bool IncludePageBreaks { get; set; }

    public string[]? Languages { get; set; }
}
