using System.Text.Json.Serialization;

namespace unstructured.rest.Models;

public class DocumentMetadata
{
    [Json<PERSON>ropertyName("title")]
    public string? Title { get; set; }

    [Json<PERSON>ropertyName("author")]
    public string? Author { get; set; }

    [Json<PERSON>ropertyName("subject")]
    public string? Subject { get; set; }

    [Json<PERSON><PERSON><PERSON><PERSON><PERSON>("creator")]
    public string? Creator { get; set; }

    [<PERSON>son<PERSON><PERSON><PERSON><PERSON><PERSON>("producer")]
    public string? Producer { get; set; }

    [JsonPropertyName("creation_date")]
    public DateTime? CreationDate { get; set; }

    [JsonPropertyName("modification_date")]
    public DateTime? ModificationDate { get; set; }

    [JsonPropertyName("file_size")]
    public long? FileSize { get; set; }

    [JsonPropertyName("file_type")]
    public string? FileType { get; set; }

    [JsonPropertyName("page_count")]
    public int? PageCount { get; set; }

    [JsonPropertyName("language")]
    public string? Language { get; set; }

    [Json<PERSON>ropertyName("keywords")]
    public string[]? Keywords { get; set; }
}
