using System.Text.Json.Serialization;

namespace unstructured.rest.Models;

public class DocumentProcessingResult
{
    [JsonPropertyName("@odata.context")]
    public string? ODataContext { get; set; } = "$metadata#docs";

    [JsonPropertyName("value")]
    public DocumentValue[] Value { get; set; } = Array.Empty<DocumentValue>();
}

public class DocumentValue
{
    [JsonPropertyName("@search.score")]
    public double SearchScore { get; set; } = 1.0;

    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("content")]
    public string Content { get; set; } = string.Empty;

    [JsonPropertyName("metadata_storage_name")]
    public string MetadataStorageName { get; set; } = string.Empty;

    [JsonPropertyName("metadata_storage_path")]
    public string MetadataStoragePath { get; set; } = string.Empty;

    [JsonPropertyName("metadata_storage_content_type")]
    public string MetadataStorageContentType { get; set; } = string.Empty;

    [JsonPropertyName("metadata_storage_size")]
    public long MetadataStorageSize { get; set; }

    [JsonPropertyName("metadata_storage_last_modified")]
    public DateTime MetadataStorageLastModified { get; set; }

    [JsonPropertyName("metadata_creation_date")]
    public DateTime? MetadataCreationDate { get; set; }

    [JsonPropertyName("metadata_title")]
    public string? MetadataTitle { get; set; }

    [JsonPropertyName("metadata_author")]
    public string? MetadataAuthor { get; set; }

    [JsonPropertyName("metadata_subject")]
    public string? MetadataSubject { get; set; }

    [JsonPropertyName("metadata_keywords")]
    public string[]? MetadataKeywords { get; set; }

    [JsonPropertyName("metadata_language")]
    public string? MetadataLanguage { get; set; }

    [JsonPropertyName("structural_elements")]
    public StructuralElement[]? StructuralElements { get; set; }

    [JsonPropertyName("page_count")]
    public int? PageCount { get; set; }

    [JsonPropertyName("processing_timestamp")]
    public DateTime ProcessingTimestamp { get; set; } = DateTime.UtcNow;
}
