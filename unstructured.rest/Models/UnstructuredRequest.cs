using System.Text.Json.Serialization;

namespace unstructured.rest.Models;

public class UnstructuredPartitionRequest
{
    [JsonPropertyName("strategy")]
    public string Strategy { get; set; } = "auto";

    [JsonPropertyName("coordinates")]
    public bool Coordinates { get; set; } = true;

    [JsonPropertyName("pdf_infer_table_structure")]
    public bool PdfInferTableStructure { get; set; } = true;

    [JsonPropertyName("xml_keep_tags")]
    public bool XmlKeepTags { get; set; } = false;

    [JsonPropertyName("encoding")]
    public string? Encoding { get; set; }

    [JsonPropertyName("include_page_breaks")]
    public bool IncludePageBreaks { get; set; } = true;

    [JsonPropertyName("languages")]
    public string[]? Languages { get; set; }
}
