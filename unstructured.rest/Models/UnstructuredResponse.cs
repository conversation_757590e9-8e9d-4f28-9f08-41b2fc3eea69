using System.Text.Json.Serialization;

namespace unstructured.rest.Models;

public class UnstructuredElement
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("element_id")]
    public string ElementId { get; set; } = string.Empty;

    [JsonPropertyName("text")]
    public string Text { get; set; } = string.Empty;

    [JsonPropertyName("metadata")]
    public UnstructuredMetadata? Metadata { get; set; }
}

public class UnstructuredMetadata
{
    [JsonPropertyName("coordinates")]
    public UnstructuredCoordinates? Coordinates { get; set; }

    [JsonPropertyName("page_number")]
    public int? PageNumber { get; set; }

    [JsonPropertyName("filename")]
    public string? Filename { get; set; }

    [JsonPropertyName("file_directory")]
    public string? FileDirectory { get; set; }

    [JsonPropertyName("filetype")]
    public string? Filetype { get; set; }

    [JsonPropertyName("languages")]
    public string[]? Languages { get; set; }

    [JsonPropertyName("parent_id")]
    public string? ParentId { get; set; }

    [JsonPropertyName("category")]
    public string? Category { get; set; }

    [JsonPropertyName("data_source")]
    public Dictionary<string, object>? DataSource { get; set; }
}

public class UnstructuredCoordinates
{
    [JsonPropertyName("points")]
    public double[][]? Points { get; set; }

    [JsonPropertyName("system")]
    public string? System { get; set; }

    [JsonPropertyName("layout_width")]
    public double? LayoutWidth { get; set; }

    [JsonPropertyName("layout_height")]
    public double? LayoutHeight { get; set; }
}
