using unstructured.rest.Services;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() {
        Title = "Unstructured Document Processing API",
        Version = "v1",
        Description = "REST API for processing documents using Unstructured.io with Azure Cognitive Search indexer-compatible output"
    });

    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

var unstructuredConfig = builder.Configuration.GetSection("UnstructuredApi");
var baseUrl = unstructuredConfig["BaseUrl"] ?? "http://localhost:8000";
var timeoutSeconds = int.Parse(unstructuredConfig["TimeoutSeconds"] ?? "300");

builder.Services.AddHttpClient<IUnstructuredService, UnstructuredService>(client =>
{
    client.BaseAddress = new Uri(baseUrl);
    client.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
});

builder.Services.AddScoped<IDocumentProcessingService, DocumentProcessingService>();

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Unstructured Document Processing API v1");
        c.RoutePrefix = "swagger";
    });
}

app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();

app.Run();