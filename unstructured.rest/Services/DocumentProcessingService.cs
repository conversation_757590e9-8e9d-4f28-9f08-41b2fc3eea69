using unstructured.rest.DTOs;
using unstructured.rest.Models;
using unstructured.rest.Utilities;

namespace unstructured.rest.Services;

public class DocumentProcessingService : IDocumentProcessingService
{
    private readonly IUnstructuredService _unstructuredService;
    private readonly ILogger<DocumentProcessingService> _logger;

    public DocumentProcessingService(
        IUnstructuredService unstructuredService, 
        ILogger<DocumentProcessingService> logger)
    {
        _unstructuredService = unstructuredService;
        _logger = logger;
    }

    public async Task<DocumentProcessingResult> ProcessDocumentAsync(
        FileUploadRequest request, 
        CancellationToken cancellationToken = default)
    {
        var validation = FileTypeValidator.ValidateFile(request.File);
        if (!validation.IsValid)
        {
            throw new ArgumentException(validation.Message);
        }

        var unstructuredRequest = new UnstructuredPartitionRequest
        {
            Strategy = request.Strategy ?? "auto",
            Coordinates = request.IncludeCoordinates,
            PdfInferTableStructure = request.InferTableStructure,
            IncludePageBreaks = request.IncludePageBreaks,
            Languages = request.Languages
        };

        UnstructuredElement[] elements;
        using (var stream = request.File.OpenReadStream())
        {
            elements = await _unstructuredService.PartitionDocumentAsync(
                stream, 
                request.File.FileName, 
                unstructuredRequest, 
                cancellationToken);
        }

        var documentValue = TransformToAzureIndexerFormat(request.File, elements);
        
        return new DocumentProcessingResult
        {
            Value = new[] { documentValue }
        };
    }

    private DocumentValue TransformToAzureIndexerFormat(IFormFile file, UnstructuredElement[] elements)
    {
        var documentId = Guid.NewGuid().ToString();
        var content = string.Join("\n\n", elements.Select(e => e.Text).Where(t => !string.IsNullOrWhiteSpace(t)));
        
        var structuralElements = elements.Select(element => new StructuralElement
        {
            Type = element.Type,
            Text = element.Text,
            PageNumber = element.Metadata?.PageNumber,
            Metadata = CreateElementMetadata(element)
        }).ToArray();

        var metadata = ExtractDocumentMetadata(elements);
        var pageCount = elements
            .Where(e => e.Metadata?.PageNumber.HasValue == true)
            .Select(e => e.Metadata!.PageNumber!.Value)
            .DefaultIfEmpty(1)
            .Max();

        return new DocumentValue
        {
            Id = documentId,
            Content = content,
            MetadataStorageName = file.FileName,
            MetadataStoragePath = $"/documents/{file.FileName}",
            MetadataStorageContentType = file.ContentType ?? FileTypeValidator.GetContentType(file.FileName),
            MetadataStorageSize = file.Length,
            MetadataStorageLastModified = DateTime.UtcNow,
            MetadataCreationDate = metadata.CreationDate,
            MetadataTitle = metadata.Title,
            MetadataAuthor = metadata.Author,
            MetadataSubject = metadata.Subject,
            MetadataKeywords = metadata.Keywords,
            MetadataLanguage = metadata.Language,
            StructuralElements = structuralElements,
            PageCount = pageCount,
            ProcessingTimestamp = DateTime.UtcNow
        };
    }



    private static Dictionary<string, object> CreateElementMetadata(UnstructuredElement element)
    {
        var metadata = new Dictionary<string, object>
        {
            ["element_id"] = element.ElementId,
            ["type"] = element.Type
        };

        if (element.Metadata?.Category != null)
            metadata["category"] = element.Metadata.Category;

        if (element.Metadata?.ParentId != null)
            metadata["parent_id"] = element.Metadata.ParentId;

        if (element.Metadata?.Languages?.Length > 0)
            metadata["languages"] = element.Metadata.Languages;

        return metadata;
    }

    private static DocumentMetadata ExtractDocumentMetadata(UnstructuredElement[] elements)
    {
        var firstElement = elements.FirstOrDefault();
        var metadata = firstElement?.Metadata;

        var languages = elements
            .Where(e => e.Metadata?.Languages?.Length > 0)
            .SelectMany(e => e.Metadata!.Languages!)
            .Distinct()
            .ToArray();

        return new DocumentMetadata
        {
            FileType = metadata?.Filetype,
            Language = languages.FirstOrDefault(),
            Keywords = languages.Length > 1 ? languages : null
        };
    }
}
