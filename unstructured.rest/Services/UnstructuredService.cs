using System.Text.Json;
using unstructured.rest.Models;

namespace unstructured.rest.Services;

public class UnstructuredService : IUnstructuredService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<UnstructuredService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public UnstructuredService(HttpClient httpClient, ILogger<UnstructuredService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
            WriteIndented = true
        };
    }

    public async Task<UnstructuredElement[]> PartitionDocumentAsync(
        Stream fileStream, 
        string fileName, 
        UnstructuredPartitionRequest request, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var content = new MultipartFormDataContent();
            
            var fileContent = new StreamContent(fileStream);
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");
            content.Add(fileContent, "files", fileName);

            AddRequestParameters(content, request);

            _logger.LogInformation("Sending partition request to Unstructured API for file: {FileName}", fileName);

            var response = await _httpClient.PostAsync("/general/v0/general", content, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Unstructured API request failed with status {StatusCode}: {ErrorContent}", 
                    response.StatusCode, errorContent);
                throw new HttpRequestException($"Unstructured API request failed: {response.StatusCode} - {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var elements = JsonSerializer.Deserialize<UnstructuredElement[]>(responseContent, _jsonOptions);

            _logger.LogInformation("Successfully processed {ElementCount} elements from file: {FileName}", 
                elements?.Length ?? 0, fileName);

            return elements ?? Array.Empty<UnstructuredElement>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document {FileName} with Unstructured API", fileName);
            throw;
        }
    }

    private static void AddRequestParameters(MultipartFormDataContent content, UnstructuredPartitionRequest request)
    {
        content.Add(new StringContent(request.Strategy), "strategy");
        content.Add(new StringContent(request.Coordinates.ToString().ToLower()), "coordinates");
        content.Add(new StringContent(request.PdfInferTableStructure.ToString().ToLower()), "pdf_infer_table_structure");
        content.Add(new StringContent(request.XmlKeepTags.ToString().ToLower()), "xml_keep_tags");
        content.Add(new StringContent(request.IncludePageBreaks.ToString().ToLower()), "include_page_breaks");

        if (!string.IsNullOrEmpty(request.Encoding))
        {
            content.Add(new StringContent(request.Encoding), "encoding");
        }

        if (request.Languages?.Length > 0)
        {
            foreach (var language in request.Languages)
            {
                content.Add(new StringContent(language), "languages");
            }
        }
    }
}
