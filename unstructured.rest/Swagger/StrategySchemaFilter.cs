using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;
using unstructured.rest.DTOs;

namespace unstructured.rest.Swagger;

public class StrategySchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (context.Type == typeof(FileUploadRequest))
        {
            var strategyProperty = schema.Properties.FirstOrDefault(p => 
                p.Key.Equals("strategy", StringComparison.OrdinalIgnoreCase));
            
            if (strategyProperty.Key != null)
            {
                strategyProperty.Value.Type = "string";
                strategyProperty.Value.Default = new OpenApiString("auto");
                strategyProperty.Value.Enum = new List<IOpenApiAny>
                {
                    new OpenApiString("auto"),
                    new OpenApiString("fast"),
                    new OpenApiString("ocr_only"),
                    new OpenApiString("hi_res")
                };
                strategyProperty.Value.Description = "Processing strategy: 'auto' (default) automatically determines the best approach, 'fast' prioritizes speed, 'ocr_only' uses OCR for scanned documents, 'hi_res' provides detailed analysis";
                strategyProperty.Value.Example = new OpenApiString("auto");
            }
        }
    }
}
