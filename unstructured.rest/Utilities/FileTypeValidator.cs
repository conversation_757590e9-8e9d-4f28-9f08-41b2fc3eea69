namespace unstructured.rest.Utilities;

public static class FileTypeValidator
{
    private static readonly long MaxFileSizeBytes = 50 * 1024 * 1024; // 50MB

    public static ValidationResult ValidateFile(IFormFile file)
    {
        if (file.Length == 0)
        {
            return new ValidationResult(false, "File is empty");
        }

        if (file.Length > MaxFileSizeBytes)
        {
            return new ValidationResult(false, $"File size exceeds maximum allowed size of {MaxFileSizeBytes / (1024 * 1024)}MB");
        }

        return new ValidationResult(true, "File is valid");
    }

    public static string GetContentType(string fileName)
    {
        return "application/octet-stream";
    }
}

public class ValidationResult
{
    public bool IsValid { get; }
    public string Message { get; }

    public ValidationResult(bool isValid, string message)
    {
        IsValid = isValid;
        Message = message;
    }
}
