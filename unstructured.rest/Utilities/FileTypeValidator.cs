namespace unstructured.rest.Utilities;

public static class FileTypeValidator
{
    private static readonly Dictionary<string, string[]> AllowedFileTypes = new()
    {
        { "application/pdf", new[] { ".pdf" } },
        { "application/vnd.openxmlformats-officedocument.wordprocessingml.document", new[] { ".docx" } },
        { "application/msword", new[] { ".doc" } },
        { "text/plain", new[] { ".txt" } },
        { "text/html", new[] { ".html", ".htm" } },
        { "text/markdown", new[] { ".md", ".markdown" } },
        { "text/csv", new[] { ".csv" } },
        { "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", new[] { ".xlsx" } },
        { "application/vnd.ms-excel", new[] { ".xls" } },
        { "application/vnd.openxmlformats-officedocument.presentationml.presentation", new[] { ".pptx" } },
        { "application/vnd.ms-powerpoint", new[] { ".ppt" } },
        { "application/rtf", new[] { ".rtf" } },
        { "application/xml", new[] { ".xml" } },
        { "text/xml", new[] { ".xml" } },
        { "application/json", new[] { ".json" } },
        { "image/jpeg", new[] { ".jpg", ".jpeg" } },
        { "image/png", new[] { ".png" } },
        { "image/tiff", new[] { ".tiff", ".tif" } }
    };

    private static readonly long MaxFileSizeBytes = 50 * 1024 * 1024; // 50MB

    public static ValidationResult ValidateFile(IFormFile file)
    {
        if (file.Length == 0)
        {
            return new ValidationResult(false, "File is empty");
        }

        if (file.Length > MaxFileSizeBytes)
        {
            return new ValidationResult(false, $"File size exceeds maximum allowed size of {MaxFileSizeBytes / (1024 * 1024)}MB");
        }

        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        var contentType = file.ContentType?.ToLowerInvariant();

        if (string.IsNullOrEmpty(extension))
        {
            return new ValidationResult(false, "File must have an extension");
        }

        var isValidType = AllowedFileTypes.Any(kvp => 
            (contentType != null && kvp.Key == contentType) || 
            kvp.Value.Contains(extension));

        if (!isValidType)
        {
            var supportedExtensions = AllowedFileTypes.SelectMany(kvp => kvp.Value).Distinct();
            return new ValidationResult(false, $"Unsupported file type. Supported extensions: {string.Join(", ", supportedExtensions)}");
        }

        return new ValidationResult(true, "File is valid");
    }

    public static string GetContentType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        var contentType = AllowedFileTypes.FirstOrDefault(kvp => kvp.Value.Contains(extension)).Key;
        return contentType ?? "application/octet-stream";
    }
}

public class ValidationResult
{
    public bool IsValid { get; }
    public string Message { get; }

    public ValidationResult(bool isValid, string message)
    {
        IsValid = isValid;
        Message = message;
    }
}
