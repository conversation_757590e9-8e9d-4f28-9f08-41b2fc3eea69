Sample Document for Testing

This is a test document to verify the Unstructured API integration.

Introduction
This document contains various types of content to test the document processing capabilities.

Main Content
The document processing service should be able to extract:
- Text content from various file formats
- Document metadata (title, author, etc.)
- Structural elements (headings, paragraphs, lists)
- Page information

Features
1. File upload validation
2. Integration with Unstructured API
3. Azure Cognitive Search indexer-compatible output format
4. Comprehensive error handling
5. Swagger/OpenAPI documentation

Conclusion
This test document should be processed successfully by the REST API and return structured data in the expected format.
